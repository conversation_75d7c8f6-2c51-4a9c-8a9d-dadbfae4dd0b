import { useNetwork } from "@/context";
import { NETWORKS } from "@/utils/contants";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { useEffect, useState } from "react";

export const useWalletAddress = () => {
  const { currentNetwork } = useNetwork();
  const [walletAddress, setWalletAddress] = useState<string>("");
  const currentAccount = useCurrentAccount();

  useEffect(() => {
    switch (currentNetwork) {
      case NETWORKS.SUI:
        setWalletAddress(currentAccount?.address || "");
        break;
      case NETWORKS.HYPEREVM:
        break;
      case NETWORKS.SOMNIA:
        break;
      default:
        setWalletAddress(currentAccount?.address || "");
        break;
    }
  }, [currentNetwork]);

  return {
    walletAddress,
  };
};
