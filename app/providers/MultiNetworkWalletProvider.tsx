"use client";

import { ReactNode } from "react";
import "@mysten/dapp-kit/dist/index.css";
import { getFullnodeUrl, SuiClient } from "@mysten/sui/client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  SuiClientProvider,
  WalletProvider,
  createNetworkConfig,
} from "@mysten/dapp-kit";
import { useNetwork } from "@/context/network";
import { NETWORKS } from "@/utils/contants";
import { HypeWalletProvider } from "./HypeWalletProvider";
import { SomniaWalletProvider } from "./SomniaWalletProvider";

const queryClient = new QueryClient();

const { networkConfig } = createNetworkConfig({
  localnet: { url: getFullnodeUrl("localnet") },
  devnet: { url: getFullnodeUrl("devnet") },
  testnet: { url: getFullnodeUrl("testnet") },
  mainnet: { url: getFullnodeUrl("mainnet") },
});

const SuiWalletProvider = ({ children }: { children: ReactNode }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <SuiClientProvider networks={networkConfig} defaultNetwork="mainnet">
        <WalletProvider autoConnect>{children}</WalletProvider>
      </SuiClientProvider>
    </QueryClientProvider>
  );
};

interface MultiNetworkWalletProviderProps {
  children: ReactNode;
}

export const MultiNetworkWalletProvider = ({
  children,
}: MultiNetworkWalletProviderProps) => {
  const { currentNetwork } = useNetwork();

  const EvmProvider = ({ children }: { children: ReactNode }) => {
    if (currentNetwork === NETWORKS.HYPEREVM) {
      return <HypeWalletProvider>{children}</HypeWalletProvider>;
    }
    if (currentNetwork === NETWORKS.SOMNIA) {
      return <SomniaWalletProvider>{children}</SomniaWalletProvider>;
    }
    return <>{children}</>;
  };

  return (
    <SuiWalletProvider>
      <EvmProvider>{children}</EvmProvider>
    </SuiWalletProvider>
  );
};
