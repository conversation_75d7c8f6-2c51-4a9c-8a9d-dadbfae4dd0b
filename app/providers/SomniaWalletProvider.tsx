"use client";

import { ReactNode } from "react";
import { PrivyProvider } from "@privy-io/react-auth";
import { somniaMainnet<PERSON>hain } from "./networkChains";

interface SomniaWalletProviderProps {
  children: ReactNode;
}

const SOMNIA_PRIVY_APP_ID =
  process.env.NEXT_PUBLIC_SOMNIA_PRIVY_APP_ID || "cmcvqsfzm0165jl0ms1i81h70";

export const SomniaWalletProvider = ({
  children,
}: SomniaWalletProviderProps) => {
  return (
    <PrivyProvider
      appId={SOMNIA_PRIVY_APP_ID}
      config={{
        loginMethods: ["wallet", "email", "google", "twitter"],
        embeddedWallets: {
          ethereum: {
            createOnLogin: "users-without-wallets",
          },
        },
        appearance: {
          walletChainType: "ethereum-only",
          theme: "dark",
          accentColor: "#676FFF",
        },
        defaultChain: somniaMainnet<PERSON>hain,
        supportedChains: [somniaMain<PERSON><PERSON>hain],
      }}
    >
      {children}
    </PrivyProvider>
  );
};
